import type {
	GetCCPatientCustomField,
	GetCCPatientType,
	PostCCPatientType,
} from "@libCC/CCTypes";
import { cleanData } from "@utils";
import { ccApiClient } from "./request";

/**
 * CC Patient Request methods
 *
 * This module contains all patient-specific API request methods for the CC platform.
 * It provides a clean interface for making patient-related API calls with proper
 * caching, error handling, and data transformation.
 */
export class CCPatientRequest {
	/**
	 * Create a new patient
	 * @param data - Patient data to create
	 * @returns Promise with created patient
	 */

	async create(data: PostCCPatientType): Promise<GetCCPatientType> {
		const response = await ccApiClient.post<{ patient: GetCCPatientType }>(
			"/patients",
			{ patient: cleanData(data) },
		);
		return response.data.patient;
	}

	/**
	 * Update an existing patient
	 * @param id - Patient ID
	 * @param data - Patient data to update
	 * @returns Promise with updated patient
	 */
	async update(id: number, data: PostCCPatientType): Promise<GetCCPatientType> {
		const response = await ccApi<PERSON>lient.put<{ patient: GetCCPatientType }>(
			`/patients/${id}`,
			{ patient: cleanData(data) },
		);
		return response.data.patient;
	}

	/**
	 * Get a patient by ID
	 * @param id - Patient ID
	 * @returns Promise with patient data
	 */
	async get(id: number): Promise<GetCCPatientType | null> {
		const response = await ccApiClient.get<{ patient: GetCCPatientType }>(
			`/patients/${id}`,
			undefined,
			`cc:patient:${id}`,
		);
		return response.data.patient || null;
	}

	/**
	 * Search for a patient by email, phone, or other identifier
	 * @param searchTerm - Search term (email, phone, etc.)
	 * @returns Promise with patient data or null
	 */
	async search(searchTerm: string): Promise<GetCCPatientType | null> {
		const response = await ccApiClient.get<{ patients: GetCCPatientType[] }>(
			`/patients`,
			{ search: searchTerm },
			`cc:patient:search:${searchTerm}`,
		);
		return response.data.patients && response.data.patients.length > 0
			? response.data.patients[0]
			: null;
	}

	/**
	 * Get all patients with pagination and filtering
	 * @param params - Query parameters for filtering and pagination
	 * @returns Promise with patients array
	 */
	async getAll(
		params: {
			page?: number;
			perPage?: number;
			active?: boolean;
			sort?: string;
		} = {
			page: 1,
			perPage: 20,
			active: true,
			sort: "-createdAt",
		},
	): Promise<GetCCPatientType[]> {
		const queryParams = {
			active: params.active,
			"page[number]": params.page,
			"page[size]": params.perPage,
			sort: params.sort,
		};

		const response = await ccApiClient.get<{ patients: GetCCPatientType[] }>(
			"/patients",
			cleanData(queryParams),
			`cc:patients:${JSON.stringify(queryParams)}`,
		);
		return response.data.patients;
	}

	/**
	 * Get patient custom fields
	 * @param ids - Array of custom field IDs
	 * @returns Promise with patient custom fields array
	 */
	async getCustomFields(ids: number[]): Promise<GetCCPatientCustomField[]> {
		const queryString = this.idsToQueryString(ids);
		const response = await ccApiClient.get<{
			patientCustomFields: GetCCPatientCustomField[];
		}>(
			`/patientCustomFields?${queryString}`,
			undefined,
			`cc:patientCustomFields:${queryString}`,
		);
		return response.data.patientCustomFields;
	}

	/**
	 * Convert array of IDs to query string format
	 * @param ids - Array of IDs
	 * @returns Query string
	 */
	private idsToQueryString(ids: number[]): string {
		return ids
			.map((id) => `ids[]=${id.toString().trim()}`)
			.join("&")
			.trim();
	}
}

// Export singleton instance
export const ccPatientRequest = new CCPatientRequest();
