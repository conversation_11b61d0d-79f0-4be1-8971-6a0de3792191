import { CCPatientRequest } from "@libCC";
// import { CCCustomFieldRequest } from "@libCC/request/customFieldRequest";

export class CustomFieldsService {
	//   private request: CCCustomFieldRequest;
	private patientRequest: CCPatientRequest;
	private LocalPatient: TLocalPatient;

	constructor(localPatient: TLocalPatient) {
		// this.request = new CCCustomFieldRequest();
		this.patientRequest = new CCPatientRequest();
		this.LocalPatient = localPatient;
	}

	async getNameValue() {
		if (!this.LocalPatient.ccData?.customFields?.length) {
			return [] as IKeyValue[];
		}
		const allFields = await this.patientRequest.getCustomFields(
			this.LocalPatient.ccData.customFields,
		);
		const returnFields: IKeyValue[] = [];
		allFields.map((field) => {
			const name = field.field.name;
			const value =
				field.values.length > 0
					? field.values.map((v) => v.value).join(", ")
					: null;
			returnFields.push({ [name]: value });
		});
		return returnFields;
	}
}
