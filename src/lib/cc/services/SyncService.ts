import type {
  GetCCPatientType,
  PostCCPatientType,
  GetCC<PERSON>atient<PERSON>ustom<PERSON>ield,
  PostCCPatientCustomfield,
  GetCCAppointmentType,
  PostCCAppointmentType,
  PutCCAppointmentType,
} from "@libCC/CCTypes";
import { LocalPatient, type TLocalPatient } from "@/lib/base/LocalPatient";
import {
  CCPatientRequest,
  CCCustomFieldRequest,
  CCAppointmentRequest,
  CCServiceRequest,
  CCUserRequest,
  CCLocationRequest,
  CCResourceRequest,
} from "@libCC/request";
import { logger, removeNullEmptyProperties } from "@/utils";
import { eq } from "drizzle-orm";

export type TLocalAppointment =
  typeof import("@database").dbSchema.appointment.$inferSelect;

/**
 * Service for syncing data from AutoPatient to CliniCore
 * Handles patients, appointments, custom fields, and related data synchronization
 */
export class CCSyncService extends LocalPatient {
  private patientRequest: CCPatientRequest;
  private customFieldRequest: CCCustomFieldRequest;
  private appointmentRequest: CCAppointmentRequest;
  private serviceRequest: CCServiceRequest;
  private userRequest: CCUserRequest;
  private locationRequest: CCLocationRequest;
  private resourceRequest: CCResourceRequest;

  constructor() {
    super();
    this.patientRequest = new CCPatientRequest();
    this.customFieldRequest = new CCCustomFieldRequest();
    this.appointmentRequest = new CCAppointmentRequest();
    this.serviceRequest = new CCServiceRequest();
    this.userRequest = new CCUserRequest();
    this.locationRequest = new CCLocationRequest();
    this.resourceRequest = new CCResourceRequest();
  }

  /**
   * Get CC custom fields label-value pairs for a patient
   */
  async getCCCustomfieldsLabelValue(
    patient: TLocalPatient
  ): Promise<Record<string, unknown>> {
    if (!patient.ccId) {
      throw new Error(
        `Patient ID not found while getting customfields, Patient ID: ${
          patient.id
        }, Email: ${patient.email ?? "NA"} and Phone: ${patient.phone ?? "NA"}`
      );
    }

    if (
      !patient.ccData?.customFields ||
      patient.ccData.customFields.length < 1
    ) {
      logger.info(`Patient doesn't have customfields`, { ccId: patient.ccId });
      return {};
    }

    const patientCustomfields = await this.patientRequest.customFields(
      patient.ccData.customFields
    );
    const labelValue: Record<string, unknown> = {};

    if (patientCustomfields.length > 0) {
      patientCustomfields.forEach((cf) => {
        const label = cf.field.label;
        const values =
          cf.values.length > 0
            ? cf.values.map((v) => v.value).join(", ")
            : null;
        labelValue[label] = values;
      });
    }

    return labelValue;
  }

  /**
   * Get patient by CC ID
   */
  async getPatientById(ccId: number): Promise<GetCCPatientType | null> {
    return await this.patientRequest.get(ccId);
  }

  /**
   * Get patient contact by ID and create/update local record
   */
  async getPatientContactById(patientId: number): Promise<TLocalPatient> {
    const patient = await this.getPatientById(patientId);
    if (!patient) {
      throw new Error(`Patient ${patientId} does not exist.`);
    }
    if (!patient.email && !patient.phoneMobile) {
      logger.error("Invalid data: Email and phone is empty", { patientId });
      throw new Error("Invalid data: Email and phone is empty.");
    }

    // Search for existing local patient or create new one
    let localPatient = await this.getPatientByCCId(patient.id);

    if (!localPatient) {
      // Create new local patient record
      const [newPatient] = await this.db
        .insert(this.dbSchema.patient)
        .values({
          email: patient.email,
          phone: patient.phoneMobile,
          ccId: patient.id,
          ccData: patient,
          ccUpdatedAt: new Date(patient.updatedAt),
        })
        .returning();
      localPatient = newPatient;
    } else {
      // Update existing record
      await this.db
        .update(this.dbSchema.patient)
        .set({
          email: patient.email,
          phone: patient.phoneMobile,
          ccData: patient,
          ccUpdatedAt: new Date(patient.updatedAt),
        })
        .where(eq(this.dbSchema.patient.id, localPatient.id));
      localPatient = await this.getPatientById(localPatient.id);
    }

    if (!localPatient) {
      throw new Error("Failed to create or retrieve patient");
    }
    return localPatient;
  }

  /**
   * Search for patient in CC or create new one
   */
  async searchPatient(patient: TLocalPatient): Promise<TLocalPatient> {
    if (!patient.phone && !patient.email) {
      throw new Error("Invalid data: Email and phone is missing");
    }

    let ccPatient: GetCCPatientType | null = null;

    if (patient.email) {
      ccPatient = await this.patientRequest.search(patient.email);
    }
    if (!ccPatient && patient.phone) {
      ccPatient = await this.patientRequest.search(patient.phone);
    }

    if (!ccPatient) {
      // Create new patient in CC
      const payload: PostCCPatientType = {
        firstName: patient.apData?.firstName,
        lastName: patient.apData?.lastName,
        email: patient.email || undefined,
        phoneMobile: patient.phone || undefined,
      };

      if (patient.apId) {
        payload.customFields = await this.syncApToCcCustomfields(patient, true);
      }

      logger.info("Creating patient in CC", { patientId: patient.id });
      ccPatient = await this.patientRequest.create(payload);
    }

    // Update local record with CC data
    await this.db
      .update(this.dbSchema.patient)
      .set({
        ccData: ccPatient,
        ccId: ccPatient.id,
        ccUpdatedAt: new Date(ccPatient.updatedAt),
      })
      .where(eq(this.dbSchema.patient.id, patient.id));

    const updatedPatient = await this.getPatientById(patient.id);
    if (!updatedPatient) {
      throw new Error("Failed to retrieve updated patient");
    }
    return updatedPatient;
  }

  /**
   * Update patient in CliniCore
   */
  async updatePatientToCC(patient: TLocalPatient): Promise<TLocalPatient> {
    if (!patient.ccId) {
      logger.info(`Patient doesn't have CC ID, creating it now`, {
        patientId: patient.id,
      });
      return await this.searchPatient(patient);
    }

    const payload: PostCCPatientType = {
      firstName: patient.apData?.firstName,
      lastName: patient.apData?.lastName,
      email: patient.email || undefined,
      phoneMobile: patient.phone || undefined,
    };

    if (patient.apId) {
      payload.customFields = await this.syncApToCcCustomfields(patient, true);
    }

    const ccPatientData = await this.patientRequest.update(
      patient.ccId,
      payload
    );

    await this.db
      .update(this.dbSchema.patient)
      .set({
        ccData: ccPatientData,
        ccUpdatedAt: new Date(ccPatientData.updatedAt),
      })
      .where(eq(this.dbSchema.patient.id, patient.id));

    const updatedPatient = await this.getPatientById(patient.id);
    if (!updatedPatient) {
      throw new Error("Failed to retrieve updated patient");
    }
    return updatedPatient;
  }

  /**
   * Create patient in CliniCore
   */
  async createPatientToCC(patient: TLocalPatient): Promise<TLocalPatient> {
    if (patient.ccId) {
      const existingPatient = await this.getPatientById(patient.id);
      if (!existingPatient) {
        throw new Error("Failed to retrieve existing patient");
      }
      return existingPatient;
    }
    return await this.searchPatient(patient);
  }

  /**
   * Sync AutoPatient custom fields to CliniCore
   */
  async syncApToCcCustomfields(
    patient: TLocalPatient,
    returnValue = false
  ): Promise<PostCCPatientCustomfield[] | undefined> {
    // This would need AP custom field service integration
    const apCustomFields: { id: string; name: string }[] = []; // await apCustomFieldService.all();
    const apCFNameValue: Record<string, unknown> = {};

    if (
      patient.apData?.customFields &&
      patient.apData.customFields.length > 0
    ) {
      patient.apData.customFields.forEach((cf) => {
        const match = apCustomFields.find((apcf) => apcf.id === cf.id);
        if (match) {
          apCFNameValue[match.name] = cf.value;
        }
      });
    }

    // Add contact info to custom fields
    apCFNameValue.email = patient.email;
    apCFNameValue.phoneMobile = patient.phone;
    apCFNameValue["phone-mobile"] = patient.phone;
    apCFNameValue.phone = patient.phone;

    const ccCustomFields = await this.customFieldRequest.all();
    const matchedProperties: PostCCPatientCustomfield[] = [];

    Object.keys(apCFNameValue).forEach((cf) => {
      const match = ccCustomFields.find(
        (ccf) => ccf.name === cf || ccf.label === cf
      );
      if (match) {
        const value: PostCCPatientCustomfield = {
          field: match,
          values: [{ value: String(apCFNameValue[cf]) }],
          patient: null,
        };

        if (match.allowedValues.length > 0) {
          const allowedValue = match.allowedValues.find(
            (v) => v.value === apCFNameValue[cf]
          );
          if (allowedValue) {
            value.values = [{ id: allowedValue.id }];
          }
        }
        matchedProperties.push(value);
      }
    });

    const payload = removeNullEmptyProperties(matchedProperties);
    if (returnValue) return payload || [];

    if (!patient.ccId) {
      logger.warn(`Patient doesn't have CC ID, preventing sync custom fields`, {
        patientId: patient.id,
      });
      return;
    }

    if (payload && payload.length > 0) {
      const ccRes = await this.patientRequest.update(patient.ccId, {
        customFields: payload,
      });
      if (ccRes) {
        await this.db
          .update(this.dbSchema.patient)
          .set({
            ccData: ccRes,
            ccUpdatedAt: new Date(ccRes.updatedAt),
          })
          .where(eq(this.dbSchema.patient.id, patient.id));
        logger.info(`Patient custom fields updated`, { patientId: patient.id });
      } else {
        logger.warn(`Unable to update patient custom fields`, {
          patientId: patient.id,
        });
      }
    }
  }

  /**
   * Create appointment in CliniCore
   */
  async createAppointmentToCC(
    appointment: TLocalAppointment
  ): Promise<TLocalAppointment | null> {
    if (appointment.ccId) {
      logger.info(`Appointment already exists in CC, dropping request`, {
        ccId: appointment.ccId,
      });
      return appointment;
    }

    if (!appointment.patientId) {
      logger.error("Patient ID missing for appointment", {
        appointmentId: appointment.id,
      });
      return null;
    }

    const patient = await this.getPatientById(appointment.patientId);
    if (!patient) {
      logger.error("Patient not found for appointment", {
        appointmentId: appointment.id,
      });
      return null;
    }

    // Ensure patient exists in CC
    const updatedPatient = await this.createPatientToCC(patient);
    if (!updatedPatient?.ccId) {
      logger.error("Unable to create or retrieve patient from CC", {
        appointmentId: appointment.id,
        patientId: patient.id,
      });
      throw new Error("Unable to create patient to CC");
    }

    const apData = appointment.apData;
    if (!apData) {
      logger.error("AP appointment data missing", {
        appointmentId: appointment.id,
      });
      return null;
    }

    const payload: PostCCAppointmentType = {
      patients: [updatedPatient.ccId],
      startsAt: apData.startTime,
      endsAt: apData.endTime,
    };

    try {
      const ccRes = await this.appointmentRequest.post(payload);

      await this.db
        .update(this.dbSchema.appointment)
        .set({
          ccData: ccRes,
          ccId: ccRes.id,
          ccUpdatedAt: new Date(ccRes.updatedAt),
        })
        .where(eq(this.dbSchema.appointment.id, appointment.id));

      const updatedAppointment = await this.getAppointmentById(appointment.id);
      if (updatedAppointment) {
        await this.syncApToCcAppointmentCustomfields(updatedAppointment);
      }

      logger.info("Appointment created in CC", {
        appointmentId: appointment.id,
        ccId: ccRes.id,
      });

      return updatedAppointment;
    } catch (error) {
      logger.error("Failed to create appointment in CC", {
        appointmentId: appointment.id,
        error: error instanceof Error ? error.message : String(error),
      });
      return null;
    }
  }

  /**
   * Sync AP appointment custom fields to CC
   */
  async syncApToCcAppointmentCustomfields(
    appointment: TLocalAppointment
  ): Promise<void> {
    // This would need AP custom field service integration
    // Implementation would map AP custom fields to CC appointment fields
    logger.info("Syncing appointment custom fields to CC", {
      appointmentId: appointment.id,
    });
  }

  /**
   * Update appointment in CliniCore
   */
  async updateAppointmentToCC(
    appointment: TLocalAppointment
  ): Promise<TLocalAppointment | null> {
    if (!appointment.ccId) {
      logger.warn(`Appointment doesn't have CC ID`, {
        appointmentId: appointment.id,
      });
      return null;
    }

    const apData = appointment.apData;
    if (!apData) {
      logger.error("AP appointment data missing", {
        appointmentId: appointment.id,
      });
      return null;
    }

    logger.info(`Appointment status: ${apData.appointmentStatus}`, {
      appointmentId: appointment.id,
    });

    if (apData.appointmentStatus === "cancelled") {
      logger.info("Canceling appointment in CC", {
        appointmentId: appointment.id,
      });
      await this.cancelAppointmentToCC(appointment);
      return appointment;
    }

    const payload: PutCCAppointmentType = {
      startsAt: apData.startTime,
      endsAt: apData.endTime,
      canceledWhy: null,
    };

    try {
      const ccRes = await this.appointmentRequest.put(
        appointment.ccId,
        payload
      );
      if (ccRes?.id) {
        await this.db
          .update(this.dbSchema.appointment)
          .set({
            ccData: ccRes,
            ccUpdatedAt: new Date(ccRes.updatedAt),
          })
          .where(eq(this.dbSchema.appointment.id, appointment.id));

        logger.info("Appointment updated in CC", {
          appointmentId: appointment.id,
          ccId: appointment.ccId,
        });

        const updatedAppointment = await this.getAppointmentById(
          appointment.id
        );
        if (updatedAppointment) {
          await this.syncApToCcAppointmentCustomfields(updatedAppointment);
        }
        return updatedAppointment;
      }
    } catch (error) {
      logger.error("Failed to update appointment in CC", {
        appointmentId: appointment.id,
        ccId: appointment.ccId,
        error: error instanceof Error ? error.message : String(error),
      });
    }

    return null;
  }

  /**
   * Cancel appointment in CliniCore
   */
  private async cancelAppointmentToCC(
    appointment: TLocalAppointment
  ): Promise<void> {
    if (!appointment.ccId || !appointment.apData) return;

    if (appointment.apData.appointmentStatus === "cancelled") {
      const ccPayload: PutCCAppointmentType = {
        canceledWhy: "cancelled at autoPatient",
      };

      try {
        const ccRes = await this.appointmentRequest.put(
          appointment.ccId,
          ccPayload
        );
        if (ccRes?.id) {
          await this.db
            .update(this.dbSchema.appointment)
            .set({
              ccData: ccRes,
              ccUpdatedAt: new Date(ccRes.updatedAt),
            })
            .where(eq(this.dbSchema.appointment.id, appointment.id));

          logger.info("Appointment cancelled in CC", {
            appointmentId: appointment.id,
            ccId: appointment.ccId,
          });
        }
      } catch (error) {
        logger.error("Failed to cancel appointment in CC", {
          appointmentId: appointment.id,
          ccId: appointment.ccId,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }
  }

  /**
   * Get appointment by ID
   */
  private async getAppointmentById(
    id: string
  ): Promise<TLocalAppointment | undefined> {
    return await this.db.query.appointment.findFirst({
      where: eq(this.dbSchema.appointment.id, id),
    });
  }
}
