/**
 * AutoPatient Custom Field Names
 * Constants for custom field names used in AutoPatient integration
 */
export const CustomFieldsNames = {
	Appointment: {
		TotalAppointment: "Total Appointments",
		LastAppointmentServices: "Last appointment services",
		LastAppointmentTreatedBy: "Last appointment treated by",
		LastAppointmentLocation: "Last appointment location",
		LastAppointmentResource: "Last appointment resource",
		LastAppointmentCategories: "Last appointment categories",
	},
	Payment: {
		LatestPaymentStatus: "Latest Payment Status",
		LatestAmountPaid: "Latest Amount Paid",
		LatestPaymentDate: "Latest Payment Date",
		LatestPaymentPDFURL: "Latest Payment PDF URL",
	},
	Invoice: {
		LatestInvoicePDFURL: "Latest Invoice PDF URL",
		LastInvoiceGrossAmount: "Latest Gross Amount",
		LastInvoiceDiscount: "Latest Discount",
		LastInvoiceTotalAmount: "Latest Total Amount",
		LatestPaymentStatus: "Latest Payment Status",
		LastInvoiceProducts: "Latest Products",
		LastInvoiceDiagnosis: "Latest Diagnosis",
		LastInvoiceTreatedBy: "Latest Treated By",
	},
	LTV: "Life Time Value",
	DueAmount: "Due amount",
	CreditAmount: "Credit amount",
	TotalInvoiceAmount: "Total Invoiced Amount",
	PatientID: "Patient ID",
};

/**
 * Legacy constants for backward compatibility
 * @deprecated Use CustomFieldsNames object instead
 */
export const apInvoiceCustomfields: Record<string, string> = {
	LatestInvoicePDFURL: CustomFieldsNames.Invoice.LatestInvoicePDFURL,
	LastInvoiceGrossAmount: CustomFieldsNames.Invoice.LastInvoiceGrossAmount,
	LastInvoiceDiscount: CustomFieldsNames.Invoice.LastInvoiceDiscount,
	LastInvoiceTotalAmount: CustomFieldsNames.Invoice.LastInvoiceTotalAmount,
	LatestPaymentStatus: CustomFieldsNames.Invoice.LatestPaymentStatus,
	LastInvoiceProducts: CustomFieldsNames.Invoice.LastInvoiceProducts,
	LastInvoiceDiagnosis: CustomFieldsNames.Invoice.LastInvoiceDiagnosis,
	LastInvoiceTreatedBy: CustomFieldsNames.Invoice.LastInvoiceTreatedBy,
};

/**
 * Legacy constants for backward compatibility
 * @deprecated Use CustomFieldsNames object instead
 */
export const apPaymentCustomfields: Record<string, string> = {
	LatestPaymentStatus: CustomFieldsNames.Payment.LatestPaymentStatus,
	LatestAmountPaid: CustomFieldsNames.Payment.LatestAmountPaid,
	LatestPaymentDate: CustomFieldsNames.Payment.LatestPaymentDate,
	LatestPaymentPDFURL: CustomFieldsNames.Payment.LatestPaymentPDFURL,
};

/**
 * Legacy constants for backward compatibility
 * @deprecated Use CustomFieldsNames object instead
 */
export const apDueAmountCustomfield: string = CustomFieldsNames.DueAmount;
export const apCreditAmountCustomfield: string = CustomFieldsNames.CreditAmount;
export const apTotalInvoiceAmountCustomfield: string =
	CustomFieldsNames.TotalInvoiceAmount;
export const apLTVCustomfield: string = CustomFieldsNames.LTV;

export default CustomFieldsNames;
