import type {
	PostAPAppointmentType,
	PutAPAppointmentType,
} from "@libAP/APTypes";
import { CustomFieldsNames } from "@libAP/CustomFieldsNames";
import { APAppointmentRequest, APNoteRequest } from "@libAP/requests";
import type { GetCCAppointmentType } from "@libCC/CCTypes";
import { eq } from "drizzle-orm";
import { DateTime } from "luxon";
import { LocalPatient, type TLocalPatient } from "@/lib/base/LocalPatient";
import { logger, removeHtmlTags } from "@/utils";

export type TLocalAppointment = typeof import("@database").dbSchema.appointment.$inferSelect;

/**
 * Service for syncing appointments between CliniCore and AutoPatient
 */
export class APAppointmentSyncService extends LocalPatient {
	private appointmentRequest: APAppointmentRequest;
	private noteRequest: APNoteRequest;

	constructor() {
		super();
		this.appointmentRequest = new APAppointmentRequest();
		this.noteRequest = new APNoteRequest();
	}

	/**
	 * Sync CC appointment to AP (create or update)
	 */
	async syncCCtoAPAppointment(appointment: TLocalAppointment): Promise<TLocalAppointment | null> {
		if (appointment.apId) {
			return await this.updateAppointmentToAP(appointment);
		}
		return await this.createAppointmentToAP(appointment);
	}

	/**
	 * Create new appointment in AutoPatient
	 */
	async createAppointmentToAP(appointment: TLocalAppointment): Promise<TLocalAppointment | null> {
		if (appointment.apId) return appointment;

		// Get the patient for this appointment
		const patient = await this.getPatientById(appointment.patientId!);
		if (!patient?.apId) {
			logger.error('Patient AP ID missing for appointment creation', {
				appointmentId: appointment.id,
				patientId: appointment.patientId,
			});
			return null;
		}

		const ccData = appointment.ccData;
		if (!ccData) {
			logger.error('CC appointment data missing', { appointmentId: appointment.id });
			return null;
		}

		const payload: PostAPAppointmentType = {
			contactId: patient.apId,
			startTime: ccData.startsAt,
			endTime: ccData.endsAt,
			appointmentStatus: ccData.canceledAt ? 'cancelled' : 'confirmed',
		};

		if (ccData.title) {
			const prefix = ccData.firstOfPatient ? 'Neukunde: ' : 'Bestandskunde: ';
			payload.title = prefix + removeHtmlTags(ccData.title);
		}

		try {
			const apRes = await this.appointmentRequest.post(payload);
			if (apRes?.id) {
				// Update local appointment record
				await this.db
					.update(this.dbSchema.appointment)
					.set({
						apId: apRes.id,
						apData: apRes,
						apUpdatedAt: new Date(),
					})
					.where(eq(this.dbSchema.appointment.id, appointment.id));

				// Add tags to contact if needed
				await this.updateContactTags(patient, ccData);

				// Create note for appointment
				const noteRes = await this.noteRequest.post(
					patient.apId,
					'Appointment Booked: ' + removeHtmlTags(ccData.title || 'No title found')
				);

				if (noteRes?.id) {
					await this.db
						.update(this.dbSchema.appointment)
						.set({
							apNoteID: noteRes.id,
						})
						.where(eq(this.dbSchema.appointment.id, appointment.id));
				}

				logger.info('Appointment created in AP', {
					appointmentId: appointment.id,
					apId: apRes.id,
				});

				// Sync appointment custom fields
				const updatedAppointment = await this.getAppointmentById(appointment.id);
				if (updatedAppointment) {
					await this.syncAppointmentCustomfieldsToAp(updatedAppointment);
				}

				return updatedAppointment;
			}
		} catch (error) {
			logger.error('Failed to create appointment in AP', {
				appointmentId: appointment.id,
				error: error instanceof Error ? error.message : String(error),
			});
		}

		return null;
	}

	/**
	 * Update existing appointment in AutoPatient
	 */
	async updateAppointmentToAP(appointment: TLocalAppointment): Promise<TLocalAppointment | null> {
		if (!appointment.apId) {
			logger.warn('Appointment AP ID missing, creating new appointment', {
				appointmentId: appointment.id,
			});
			return await this.createAppointmentToAP(appointment);
		}

		const ccData = appointment.ccData;
		if (!ccData) {
			logger.error('CC appointment data missing', { appointmentId: appointment.id });
			return null;
		}

		const payload: PutAPAppointmentType = {
			startTime: ccData.startsAt,
			endTime: ccData.endsAt,
			appointmentStatus: ccData.canceledAt ? 'cancelled' : 'confirmed',
		};

		if (ccData.title) {
			const prefix = ccData.firstOfPatient ? 'Neukunde: ' : 'Bestandskunde: ';
			payload.title = prefix + removeHtmlTags(ccData.title);
		}

		try {
			const apRes = await this.appointmentRequest.put(appointment.apId, payload);
			if (apRes?.id) {
				await this.db
					.update(this.dbSchema.appointment)
					.set({
						apData: apRes,
						apUpdatedAt: new Date(),
					})
					.where(eq(this.dbSchema.appointment.id, appointment.id));

				logger.info('Appointment updated in AP', {
					appointmentId: appointment.id,
					apId: apRes.id,
				});

				// Update note if exists
				if (appointment.apNoteID) {
					await this.updateAppointmentNote(appointment);
				}

				return await this.getAppointmentById(appointment.id);
			}
		} catch (error) {
			logger.error('Failed to update appointment in AP', {
				appointmentId: appointment.id,
				apId: appointment.apId,
				error: error instanceof Error ? error.message : String(error),
			});
		}

		return null;
	}

	/**
	 * Delete appointment from AutoPatient
	 */
	async deleteAppointmentFromAP(appointment: TLocalAppointment): Promise<boolean> {
		if (appointment.apId) {
			try {
				await this.appointmentRequest.delete(appointment.apId);
				logger.info('Appointment deleted from AP', {
					appointmentId: appointment.id,
					apId: appointment.apId,
				});
				return true;
			} catch (error) {
				logger.error('Failed to delete appointment from AP', {
					appointmentId: appointment.id,
					apId: appointment.apId,
					error: error instanceof Error ? error.message : String(error),
				});
			}
		} else {
			logger.warn('Appointment does not exist in AP', {
				appointmentId: appointment.id,
			});
		}
		return false;
	}

	/**
	 * Sync appointment custom fields to AutoPatient
	 */
	async syncAppointmentCustomfieldsToAp(appointment: TLocalAppointment): Promise<void> {
		const ccData = appointment.ccData;
		if (!ccData) return;

		const fieldsPayload: Record<string, string> = {
			[CustomFieldsNames.Appointment.LastAppointmentServices]: 'Unknown',
			[CustomFieldsNames.Appointment.LastAppointmentTreatedBy]: 'Unknown',
			[CustomFieldsNames.Appointment.LastAppointmentLocation]: 'Unknown',
			[CustomFieldsNames.Appointment.LastAppointmentResource]: 'Unknown',
			[CustomFieldsNames.Appointment.LastAppointmentCategories]: 'Unknown',
		};

		// These would need to be implemented with proper CC service integration
		// For now, we'll set placeholder values
		
		// Get patient for this appointment
		const patient = await this.getPatientById(appointment.patientId!);
		if (patient) {
			// This would call the sync service to update custom fields
			// await syncService.syncCCtoAPCustomfields(patient, fieldsPayload);
		}
	}

	/**
	 * Update contact tags based on appointment data
	 */
	private async updateContactTags(patient: TLocalPatient, ccData: GetCCAppointmentType): Promise<void> {
		const tags: string[] = [];

		// Add service-based tags (would need CC service integration)
		if (ccData.services && ccData.services.length > 0) {
			// This would fetch service names and add them as tags
		}

		if (ccData.firstOfPatient) {
			tags.push('New patient');
		}

		if (tags.length > 0 && patient.apId) {
			try {
				const updatedContact = await this.contactRequest.update(patient.apId, {
					tags: [...tags, ...(patient.apData?.tags ?? [])],
				});
				
				if (updatedContact?.id) {
					await this.db
						.update(this.dbSchema.patient)
						.set({
							apData: updatedContact,
							apUpdatedAt: new Date(),
						})
						.where(eq(this.dbSchema.patient.id, patient.id));
				}
			} catch (error) {
				logger.error('Failed to update contact tags', {
					patientId: patient.id,
					apId: patient.apId,
					error: error instanceof Error ? error.message : String(error),
				});
			}
		}
	}

	/**
	 * Update appointment note
	 */
	private async updateAppointmentNote(appointment: TLocalAppointment): Promise<void> {
		if (!appointment.apNoteID || !appointment.ccData) return;

		const patient = await this.getPatientById(appointment.patientId!);
		if (!patient?.apId) return;

		const ccData = appointment.ccData;
		let noteContent: string;

		if (ccData.canceledAt) {
			noteContent = `Appointment Canceled: ${removeHtmlTags(ccData.title || 'No title found')}\n\n` +
				`Canceled At: ${DateTime.fromISO(ccData.canceledAt).toLocaleString(DateTime.DATETIME_FULL)}\n\n` +
				`Canceled Reason: ${ccData.canceledWhy || 'No reason provided'}`;
		} else {
			noteContent = `Appointment Booked: ${removeHtmlTags(ccData.title || 'No title found')}`;
		}

		try {
			await this.noteRequest.put(patient.apId, appointment.apNoteID, noteContent);
			logger.info('Appointment note updated', {
				appointmentId: appointment.id,
				noteId: appointment.apNoteID,
			});
		} catch (error) {
			logger.error('Failed to update appointment note', {
				appointmentId: appointment.id,
				noteId: appointment.apNoteID,
				error: error instanceof Error ? error.message : String(error),
			});
		}
	}

	/**
	 * Get appointment by ID
	 */
	private async getAppointmentById(id: string): Promise<TLocalAppointment | undefined> {
		return await this.db.query.appointment.findFirst({
			where: eq(this.dbSchema.appointment.id, id),
		});
	}

	// Import contact request for tag updates
	private contactRequest = new (await import("@libAP/requests")).APContactRequest();
}
