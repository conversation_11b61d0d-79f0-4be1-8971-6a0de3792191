import type {
	GetAP<PERSON><PERSON>actType,
	PostAPContactType,
	UpdateAPCustomfields,
	GetAPAppointmentType,
	PostAPAppointmentType,
	PutAPAppointmentType,
} from "@libAP/APTypes";
import type { GetCCPatientType, GetCCAppointmentType } from "@libCC/CCTypes";
import { LocalPatient, type TLocalPatient } from "@/lib/base/LocalPatient";
import { APContactRequest, APCustomFieldRequest, APAppointmentRequest, APNoteRequest } from "@libAP/requests";
import { 
	apInvoiceCustomfields, 
	apPaymentCustomfields, 
	apLTVCustomfield,
	CustomFieldsNames 
} from "@libAP/CustomFieldsNames";
import { logger, removeHtmlTags, cLog } from "@/utils";
import { eq } from "drizzle-orm";

/**
 * Service for syncing data between CliniCore and AutoPatient
 * Handles custom fields, appointments, contacts, and related data synchronization
 */
export class APSyncService extends LocalPatient {
	private contactRequest: APContactRequest;
	private customFieldRequest: APCustomFieldRequest;
	private appointmentRequest: APAppointmentRequest;
	private noteRequest: APNoteRequest;

	constructor() {
		super();
		this.contactRequest = new APContactRequest();
		this.customFieldRequest = new APCustomFieldRequest();
		this.appointmentRequest = new APAppointmentRequest();
		this.noteRequest = new APNoteRequest();
	}

	/**
	 * Sync CliniCore custom fields to AutoPatient
	 * @param patient - Local patient record
	 * @param extraFields - Additional fields to sync
	 */
	async syncCCtoAPCustomfields(patient: TLocalPatient, extraFields: Record<string, unknown> = {}): Promise<void> {
		if (!patient.apId || !patient.ccId) {
			throw new Error(
				'AP Contact ID or CC Patient ID is missing, unable to sync custom fields'
			);
		}

		// Get CC custom fields (this would need to be implemented with CC service)
		let ccNameValue = await this.getCCCustomfieldsLabelValue(patient);
		if (extraFields) {
			ccNameValue = { ...ccNameValue, ...extraFields };
		}

		// Add computed fields
		ccNameValue[CustomFieldsNames.Appointment.TotalAppointment] = patient.ccData?.appointments?.length || 0;
		ccNameValue[CustomFieldsNames.PatientID] = patient.ccData?.id;

		// Get service-specific data
		const services = await this.getIndividualServiceAppointmentCount(patient);
		const spends = await this.getIndividualServiceSpends(patient);

		if (Object.keys(services).length > 0) {
			ccNameValue = { ...ccNameValue, ...services };
		}
		if (Object.keys(spends).length > 0) {
			ccNameValue = { ...ccNameValue, ...spends };
		}

		const apNameId = await this.getApCustomfieldsNameId();
		const payload: PostAPContactType = {
			customFields: [],
		};

		if (Object.keys(ccNameValue).length > 0) {
			for (const [key, value] of Object.entries(ccNameValue)) {
				if (key in apNameId && payload.customFields) {
					payload.customFields.push({
						id: apNameId[key],
						value: value as string | number,
					});
				} else {
					logger.info(`No AP Custom Field found with Name ${key}, Creating it now.`);
					const cfRes = await this.customFieldRequest.create({
						name: key,
						dataType: 'TEXT',
					});
					if (cfRes?.id) {
						payload.customFields?.push({
							id: cfRes.id,
							value: value as string | number,
						});
					}
				}
			}
		}

		if (payload.customFields && payload.customFields.length > 0) {
			const updateRes = await this.contactRequest.update(patient.apId, payload);
			if (updateRes) {
				await this.db
					.update(this.dbSchema.patient)
					.set({
						apData: updateRes,
						apUpdatedAt: new Date(),
					})
					.where(eq(this.dbSchema.patient.id, patient.id));
				logger.info(`CC to AP Customfields synced`, { contactId: patient.apId });
			}
		} else {
			logger.info(`No customfields found to sync`, { contactId: patient.apId });
		}
	}

	/**
	 * Get AP custom field name to ID mapping
	 */
	async getApCustomfieldsNameId(): Promise<Record<string, string>> {
		const cf = await this.customFieldRequest.all();
		const nameId: Record<string, string> = {};
		if (cf.length > 0) {
			cf.forEach((c) => (nameId[c.name] = c.id));
		}
		return nameId;
	}

	/**
	 * Update or create contact in AutoPatient
	 * @param patient - Local patient record
	 * @param syncCustomfields - Whether to sync custom fields
	 */
	async updateOrCreateContact(
		patient: TLocalPatient,
		syncCustomfields = true
	): Promise<TLocalPatient> {
		if (!patient.ccData) {
			throw new Error('Required CC data is missing while creating contact');
		}
		if (!patient.email && !patient.phone) {
			throw new Error('Invalid contact data, email and phone is missing.');
		}

		const payload: PostAPContactType = {
			email: patient.email,
			phone: patient.phone,
			firstName: patient.ccData.firstName,
			lastName: patient.ccData.lastName,
			tags: ['cc_api'],
			dateOfBirth: patient.ccData.dob,
		};

		let apContact: GetAPContactType;
		if (!patient.apId) {
			payload.source = 'cc';
			payload.gender = patient.ccData.gender;
			apContact = await this.contactRequest.upsert(payload);
		} else {
			payload.tags = [...(payload.tags ?? []), ...(patient.apData?.tags ?? [])];
			apContact = await this.contactRequest.update(patient.apId, payload);
		}

		if (apContact) {
			await this.db
				.update(this.dbSchema.patient)
				.set({
					apData: apContact,
					apId: apContact.id,
					apUpdatedAt: new Date(apContact.dateUpdated || new Date()),
				})
				.where(eq(this.dbSchema.patient.id, patient.id));
			
			logger.info(`Contact synced to AP`, { 
				patientId: patient.id, 
				apId: apContact.id 
			});
		} else {
			logger.error('Unable to create/update contact to AP', { 
				ccData: patient.ccData 
			});
			throw new Error('Unable to create/update contact to AP');
		}

		if (syncCustomfields) {
			const updatedPatient = await this.getPatientById(patient.id);
			if (updatedPatient) {
				await this.syncCCtoAPCustomfields(updatedPatient);
			}
		}

		// Sync invoice and payments would be called here
		// await this.syncInvoicePayments(updatedPatient);

		return (await this.getPatientById(patient.id))!;
	}

	/**
	 * Get AP custom field value by name for a contact
	 */
	async getAPCustomFieldValueByName(contactId: string, fieldName: string): Promise<string | null> {
		const contact = await this.contactRequest.get(contactId);
		const fId = await this.getAPCustomFieldIdByName(fieldName);
		return contact.customFields && contact.customFields.length > 0 && fId
			? contact.customFields.find((cf) => cf.id === fId)?.value?.toString() ?? null
			: null;
	}

	/**
	 * Get AP custom field ID by name, creating if it doesn't exist
	 */
	async getAPCustomFieldIdByName(name: string): Promise<string> {
		const fields = await this.customFieldRequest.all();
		const existingField = fields.find((cf) => cf.name === name);
		
		if (existingField) {
			return existingField.id;
		}
		
		const newField = await this.customFieldRequest.create({ name, dataType: 'TEXT' });
		return newField.id;
	}

	/**
	 * Update AP custom fields for a patient
	 */
	async updateApCustomfields(
		patient: TLocalPatient,
		customfields: UpdateAPCustomfields[]
	): Promise<TLocalPatient> {
		if (!patient.apId) {
			logger.warn(`Contact AP Id is missing`, { ccId: patient.ccId });
			return patient;
		}

		const apCustomfields = await this.customFieldRequest.all();
		const payload: { id: string; value: string | number }[] = [];

		if (customfields.length > 0 && apCustomfields.length > 0) {
			for (const cf of customfields) {
				const match = apCustomfields.find((apcf) => apcf.name === cf.name);
				if (match) {
					payload.push({ id: match.id, value: cf.value });
				} else {
					const create = await this.customFieldRequest.create({ 
						name: cf.name, 
						dataType: 'TEXT' 
					});
					if (create?.id) {
						payload.push({ id: create.id, value: cf.value });
					}
				}
			}
		}

		if (payload.length > 0) {
			const apRes = await this.contactRequest.update(patient.apId, { 
				customFields: payload 
			});
			if (apRes?.id) {
				await this.db
					.update(this.dbSchema.patient)
					.set({
						apData: apRes,
						apUpdatedAt: new Date(),
					})
					.where(eq(this.dbSchema.patient.id, patient.id));
			}
		}

		return (await this.getPatientById(patient.id))!;
	}

	// Placeholder methods that would need CC service integration
	private async getCCCustomfieldsLabelValue(patient: TLocalPatient): Promise<Record<string, unknown>> {
		// This would integrate with CC service to get custom field values
		return {};
	}

	private async getIndividualServiceAppointmentCount(patient: TLocalPatient): Promise<Record<string, number>> {
		// This would calculate service appointment counts
		return {};
	}

	private async getIndividualServiceSpends(patient: TLocalPatient): Promise<Record<string, number>> {
		// This would calculate service spending amounts
		return {};
	}
}
