import type { GetAPContactType, PostAPContactType } from "@libAP/APTypes";
import { APContactRequest } from "@libAP/requests/contactRequest";
import { eq } from "drizzle-orm";
import { LocalPatient, type TLocalPatient } from "@/lib/base/LocalPatient";

export class ContactService extends LocalPatient {
	private request: APContactRequest;

	constructor() {
		super();
		this.request = new APContactRequest();
	}

	async sync(contact: GetAPContactType) {
		const localContact = await this.getPatientByAPId(contact.id);
		if (!localContact) {
			await this.upsertLocalFromAP(contact);
		}
		return this;
	}

	async upsertToAP(patient: TLocalPatient) {
		const payload: PostAPContactType = {
			email: patient.email,
			phone: patient.phone,
			firstName: patient.ccData?.firstName,
			lastName: patient.ccData?.lastName,
			tags: ["cc_api"],
			dateOfBirth: patient.ccData?.dob,
		};
		const apContact = await this.request.upsert(payload);
		patient.apData = apContact;
		patient.apId = apContact.id;
		await this.db
			.update(this.dbSchema.patient)
			.set({
				apId: patient.apId,
				apData: patient.apData,
				apUpdatedAt: new Date(apContact.dateUpdated || new Date()),
			})
			.where(eq(this.dbSchema.patient.id, patient.id));
		this.log.info("Patient has been synced.", {
			patientId: patient.id,
			apID: apContact.id,
			email: patient.email,
			phone: patient.phone,
		});
		return this;
	}

	async getPatientFromAPByID(id: string) {
		const contact = await this.request.get(id);
		contact && (await this.upsertLocalFromAP(contact));
		return this.patient;
	}

	async searchContactInAP(query: string) {
		const contacts = await this.request.searchContacts(query, 1);
		contacts && (await this.upsertLocalFromAP(contacts[0]));
		return this.patient;
	}
}
