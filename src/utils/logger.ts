/**
 * Beautiful logger utility with colored output and readable formatting
 * Perfect for development and debugging
 */

export interface LogContext {
	[key: string]: unknown;
}

// ANSI color codes for terminal styling
const colors = {
	reset: "\x1b[0m",
	bright: "\x1b[1m",
	dim: "\x1b[2m",
	red: "\x1b[31m",
	green: "\x1b[32m",
	yellow: "\x1b[33m",
	blue: "\x1b[34m",
	magenta: "\x1b[35m",
	cyan: "\x1b[36m",
	white: "\x1b[37m",
	gray: "\x1b[90m",
	bgRed: "\x1b[41m",
	bgGreen: "\x1b[42m",
	bgYellow: "\x1b[43m",
	bgBlue: "\x1b[44m",
	bgMagenta: "\x1b[45m",
};

// Helper function to format timestamp
const formatTimestamp = (): string => {
	const now = new Date();
	return `${colors.gray}[${now.toLocaleTimeString()}]${colors.reset}`;
};

// Helper function to format log level with colors
const formatLevel = (level: string): string => {
	const levelColors = {
		INFO: `${colors.bgBlue}${colors.white} INFO ${colors.reset}`,
		WARN: `${colors.bgYellow}${colors.white} WARN ${colors.reset}`,
		ERROR: `${colors.bgRed}${colors.white} ERROR ${colors.reset}`,
		DEBUG: `${colors.bgMagenta}${colors.white} DEBUG ${colors.reset}`,
		SUCCESS: `${colors.bgGreen}${colors.white} SUCCESS ${colors.reset}`,
	};
	return levelColors[level as keyof typeof levelColors] || level;
};

// Helper function to pretty print objects
const formatObject = (obj: unknown, indent = 0): string => {
	if (obj === null) return `${colors.gray}null${colors.reset}`;
	if (obj === undefined) return `${colors.gray}undefined${colors.reset}`;

	if (typeof obj === "string") {
		return `${colors.green}"${obj}"${colors.reset}`;
	}

	if (typeof obj === "number") {
		return `${colors.cyan}${obj}${colors.reset}`;
	}

	if (typeof obj === "boolean") {
		return `${colors.yellow}${obj}${colors.reset}`;
	}

	if (Array.isArray(obj)) {
		if (obj.length === 0) return "[]";
		const items = obj
			.map((item) => "  ".repeat(indent + 1) + formatObject(item, indent + 1))
			.join(",\n");
		return `[\n${items}\n${"  ".repeat(indent)}]`;
	}

	if (typeof obj === "object") {
		const entries = Object.entries(obj);
		if (entries.length === 0) return "{}";

		const items = entries
			.map(([key, value]) => {
				const formattedKey = `${colors.blue}${key}${colors.reset}`;
				const formattedValue = formatObject(value, indent + 1);
				return `${"  ".repeat(indent + 1)}${formattedKey}: ${formattedValue}`;
			})
			.join(",\n");

		return `{\n${items}\n${"  ".repeat(indent)}}`;
	}

	return String(obj);
};

// Helper function to create separator lines
const createSeparator = (char = "─", length = 60): string => {
	return `${colors.gray}${char.repeat(length)}${colors.reset}`;
};

export const logger = {
	info: (message: string, context?: LogContext) => {
		const timestamp = formatTimestamp();
		const level = formatLevel("INFO");

		console.log(
			`${timestamp} ${level} ${colors.white}${message}${colors.reset}`,
		);

		if (context && Object.keys(context).length > 0) {
			console.log(`${colors.gray}Context:${colors.reset}`);
			console.log(formatObject(context));
			console.log(createSeparator());
		}
	},

	warn: (message: string, context?: LogContext) => {
		const timestamp = formatTimestamp();
		const level = formatLevel("WARN");

		console.warn(
			`${timestamp} ${level} ${colors.yellow}${message}${colors.reset}`,
		);

		if (context && Object.keys(context).length > 0) {
			console.warn(`${colors.gray}Context:${colors.reset}`);
			console.warn(formatObject(context));
			console.warn(createSeparator());
		}
	},

	error: (message: string, context?: LogContext) => {
		const timestamp = formatTimestamp();
		const level = formatLevel("ERROR");

		console.error(
			`${timestamp} ${level} ${colors.red}${message}${colors.reset}`,
		);

		if (context && Object.keys(context).length > 0) {
			console.error(`${colors.gray}Context:${colors.reset}`);
			console.error(formatObject(context));
			console.error(createSeparator());
		}
	},

	debug: (message: string, context?: LogContext) => {
		const timestamp = formatTimestamp();
		const level = formatLevel("DEBUG");

		console.debug(
			`${timestamp} ${level} ${colors.magenta}${message}${colors.reset}`,
		);

		if (context && Object.keys(context).length > 0) {
			console.debug(`${colors.gray}Context:${colors.reset}`);
			console.debug(formatObject(context));
			console.debug(createSeparator());
		}
	},

	success: (message: string, context?: LogContext) => {
		const timestamp = formatTimestamp();
		const level = formatLevel("SUCCESS");

		console.log(
			`${timestamp} ${level} ${colors.green}${message}${colors.reset}`,
		);

		if (context && Object.keys(context).length > 0) {
			console.log(`${colors.gray}Context:${colors.reset}`);
			console.log(formatObject(context));
			console.log(createSeparator());
		}
	},

	// Special methods for specific use cases
	patient: (
		action: string,
		patient: { id?: number | string; name?: string; email?: string },
		context?: LogContext,
	) => {
		const timestamp = formatTimestamp();
		const patientInfo = `${colors.cyan}Patient${colors.reset} ${colors.bright}${
			patient.name || "Unknown"
		}${colors.reset} ${colors.gray}(ID: ${patient.id || "N/A"})${colors.reset}`;

		console.log(
			`${timestamp} ${formatLevel("INFO")} ${colors.blue}${action}${
				colors.reset
			} ${patientInfo}`,
		);

		if (patient.email) {
			console.log(
				`  ${colors.gray}Email:${colors.reset} ${colors.green}${patient.email}${colors.reset}`,
			);
		}

		if (context && Object.keys(context).length > 0) {
			console.log(`${colors.gray}Details:${colors.reset}`);
			console.log(formatObject(context));
			console.log(createSeparator());
		}
	},

	sync: (
		direction: "CC→AP" | "AP→CC",
		entity: string,
		id: string | number,
		status: "started" | "completed" | "failed",
		context?: LogContext,
	) => {
		const timestamp = formatTimestamp();
		const directionColor = direction === "CC→AP" ? colors.blue : colors.magenta;
		const statusColor =
			status === "completed"
				? colors.green
				: status === "failed"
					? colors.red
					: colors.yellow;

		console.log(
			`${timestamp} ${formatLevel("INFO")} ${directionColor}${direction}${
				colors.reset
			} ${colors.white}${entity}${colors.reset} ${colors.gray}(${id})${
				colors.reset
			} ${statusColor}${status.toUpperCase()}${colors.reset}`,
		);

		if (context && Object.keys(context).length > 0) {
			console.log(formatObject(context));
			console.log(createSeparator());
		}
	},

	webhook: (platform: "CC" | "AP", event: string, payload?: unknown) => {
		const timestamp = formatTimestamp();
		const platformColor = platform === "CC" ? colors.blue : colors.magenta;

		console.log(
			`${timestamp} ${formatLevel("INFO")} ${colors.yellow}📡 WEBHOOK${
				colors.reset
			} ${platformColor}${platform}${colors.reset} ${colors.white}${event}${
				colors.reset
			}`,
		);

		if (payload) {
			console.log(`${colors.gray}Payload:${colors.reset}`);
			console.log(formatObject(payload));
			console.log(createSeparator());
		}
	},

	table: (title: string, data: Record<string, unknown>[]) => {
		const timestamp = formatTimestamp();
		console.log(
			`${timestamp} ${formatLevel("INFO")} ${colors.bright}${title}${
				colors.reset
			}`,
		);
		console.table(data);
		console.log(createSeparator());
	},
};
