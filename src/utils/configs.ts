/**
 * Application configuration interface
 */
interface AppConfigs {
	databaseUrl: string;
	ccApiDomain: string;
	ccApiKey: string;
	apApiKey: string;
	apApiDomain: string;
	cacheTTL: number;
	maxRetries: number;
	requestTimeout: number;
	locationID: string;
	syncBufferTimeSec: number; // Buffer time in seconds to prevent unnecessary syncs
	apCalendarId: string;
}

const configs: AppConfigs = {
	databaseUrl:
		"postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require",
	ccApiDomain: "https://ccdemo.clinicore.eu/api/v1",
	ccApiKey:
		"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
	apApiDomain: "https://services.leadconnectorhq.com",
	apApiKey: "pit-19f7d314-acb6-45d7-bae7-2a56ccf789b6",
	cacheTTL: 300, // 5 minutes
	maxRetries: 3,
	requestTimeout: 30000, // 30 seconds
	locationID: "CIY0QcIvP7m9TxVWlvy3",
	syncBufferTimeSec: 60, // 1 minute buffer time to prevent unnecessary syncs
	apCalendarId: "ZsyisrZNAGBZsLAE60zj",
};

/**
 * Get a specific configuration value
 * @param key - Configuration key to retrieve
 * @returns The configuration value
 */
export const getConfig = (key: keyof AppConfigs): AppConfigs[typeof key] => {
	const v = configs[key] as AppConfigs[typeof key];
	if (!v) {
		console.error(`Config ${key} is not defined.`);
		throw new Error(`Config ${key} is not defined.`);
	}
	return v;
};

/**
 * Get all configuration values
 * @returns Complete configuration object
 */
const getConfigs = (): AppConfigs => {
	return configs;
};

export default getConfig;
export { getConfigs };
export type { AppConfigs };
